NODE_ENV=production
PORT=3000
SECRET_KEY=fpt-secret-key
DEBUG=true
MONGODB_URL_READ_MODEL=mongodb://*********:27017/fpt-msx-commission-readmodel
MONGODB_URL_EVENT_STORE=mongodb://*********:27017/fpt-msx-commission-eventstore
DEMAND_MONGODB_URL_READ_MODEL=mongodb://*********:27017/fpt-msx-demand-readmodel?authSource=admin

RABBITMQ_URL=***************************************
REDIS_URL=redis://*********:6379

# Set to your preferred expiry period
EXPIRY_TIME_SECONDS = 300
# Set to a number >=10 if you want the chart to autorefresh
AUTOREFRESH_TIME_SECONDS = 30
NUMBER_DAY_TODAY = 30

SVC_NAME=msx-property-ihz
PROPERTY_UNIT_PUBLISH_TIME_MINUTE = 120
API_PREFIX=/msx-property-ihz/api
DOCS_PREFIX=msx-property-ihz/docs
CONTRACT_URI=http://*********:3033

PROPERTY_UNIT_CANCEL_REGISTERED_TIME_OUT=5 PROPERTY_UNIT_CANCEL_REGISTERED_FROM_HOUR=1 PROPERTY_UNIT_CANCEL_REGISTERED_TO_HOUR=14
PROPERTY_UNIT_NEXT_TIME_REGISTERED_DURATION=0

PROPERTY_UNIT_DWELL_TIME=60
PROPERTY_UNIT_TIME_BOOKING_PRIORITY=2880
DATE_DEBT_REMINDER=2
REDIS_HOST=*********
REDIS_PORT=6379
CACHE_TTL=60
METABASE_SITE_URL=enter_value_here 
METABASE_SECRET_KEY=enter_value_here
METABASE_EXPIRE_TIME_MINUTE=10
METABASE_DASHBOARD_NUMBER=12
ORGCHART_URI=\"http://localhost:68/msx-orgchart\"
API_INTERNAL_ID=
API_INTERNAL_SECRET=